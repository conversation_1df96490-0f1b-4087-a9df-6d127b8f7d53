/// -----
/// safety_exam_bloc.dart
/// 
/// 安全教育考试BLoC类
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/domain/usecases/get_exam_questions_usecase.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_event.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_state.dart';

/// 安全教育考试BLoC
///
/// 处理安全教育考试相关的业务逻辑
class SafetyExamBloc extends Bloc<SafetyExamEvent, SafetyExamState> {
  /// 获取安全教育考试题目用例
  final GetExamQuestionsUseCase getExamQuestionsUseCase;

  /// 构造函数
  SafetyExamBloc({
    required this.getExamQuestionsUseCase,
  }) : super(SafetyExamInitial()) {
    on<LoadExamQuestionsEvent>(_onLoadExamQuestions);
    on<SelectAnswerEvent>(_onSelectAnswer);
    on<NextQuestionEvent>(_onNextQuestion);
    on<ViewResultsEvent>(_onViewResults);
    on<JumpToQuestionEvent>(_onJumpToQuestion);
  }

  /// 处理加载安全教育考试题目事件
  Future<void> _onLoadExamQuestions(
    LoadExamQuestionsEvent event,
    Emitter<SafetyExamState> emit,
  ) async {
    emit(SafetyExamLoading());
    try {
      final questions = await getExamQuestionsUseCase();
      if (questions.isEmpty) {
        emit(const SafetyExamLoadFailure(message: '没有可用的试题'));
      } else {
        emit(SafetyExamInProgress(
          questions: questions,
          currentQuestionIndex: 0,
          userAnswers: {},
          correctCount: 0,
          incorrectCount: 0,
        ));
      }
    } catch (e) {
      emit(SafetyExamLoadFailure(message: '加载试题失败: ${e.toString()}'));
    }
  }

  /// 处理选择答案事件
  void _onSelectAnswer(
    SelectAnswerEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      final currentQuestion = currentState.currentQuestion;
      
      // 如果已经回答过这个问题，不做任何处理
      if (currentState.userAnswers.containsKey(currentQuestion.id)) {
        return;
      }

      // 判断答案是否正确
      final isCorrect = event.selectedOptionId == currentQuestion.correctAnswer;
      
      // 更新用户答案映射
      final updatedUserAnswers = Map<String, String>.from(currentState.userAnswers);
      updatedUserAnswers[currentQuestion.id] = event.selectedOptionId;
      
      // 更新正确和错误题目计数
      int correctCount = currentState.correctCount;
      int incorrectCount = currentState.incorrectCount;
      
      if (isCorrect) {
        correctCount++;
      } else {
        incorrectCount++;
      }
      
      // 发出新状态
      emit(currentState.copyWith(
        userAnswers: updatedUserAnswers,
        selectedOptionId: event.selectedOptionId,
        correctCount: correctCount,
        incorrectCount: incorrectCount,
      ));
    }
  }

  /// 处理下一题事件
  void _onNextQuestion(
    NextQuestionEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      
      // 如果当前题目未回答，不进行下一题操作
      if (!currentState.isCurrentQuestionAnswered) {
        return;
      }
      
      // 如果是最后一题，不做任何处理
      if (currentState.isLastQuestion) {
        return;
      }
      
      // 发出新状态，前进到下一题
      emit(currentState.copyWith(
        currentQuestionIndex: currentState.currentQuestionIndex + 1,
        selectedOptionId: null,
      ));
    }
  }

  /// 处理查看结果事件
  void _onViewResults(
    ViewResultsEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      
      // 如果当前题目未回答，不进行查看结果操作
      if (!currentState.isCurrentQuestionAnswered) {
        return;
      }
      
      // 发出考试完成状态
      emit(SafetyExamCompleted(
        questions: currentState.questions,
        userAnswers: currentState.userAnswers,
        correctCount: currentState.correctCount,
        incorrectCount: currentState.incorrectCount,
      ));
    }
  }
  
  /// 处理跳转到指定题目事件
  void _onJumpToQuestion(
    JumpToQuestionEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      
      // 验证题目索引是否有效
      if (event.questionIndex < 0 || event.questionIndex >= currentState.questions.length) {
        return;
      }
      
      // 发出新状态，跳转到指定题目
      emit(currentState.copyWith(
        currentQuestionIndex: event.questionIndex,
        selectedOptionId: null,
      ));
    }
  }
}
