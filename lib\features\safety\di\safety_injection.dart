/// -----
/// safety_injection.dart
/// 
/// 安全教育模块依赖注入
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/safety/data/datasources/safety_exam_data_source.dart';
import 'package:flutter_demo/features/safety/data/datasources/remote_safety_exam_data_source.dart';
import 'package:flutter_demo/features/safety/data/datasources/debug_safety_exam_data_source.dart';
import 'package:flutter_demo/features/safety/data/repositories/safety_exam_repository_impl.dart';
import 'package:flutter_demo/features/safety/domain/repositories/safety_exam_repository.dart';
import 'package:flutter_demo/features/safety/domain/usecases/get_exam_questions_usecase.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_bloc.dart';

/// 获取依赖注入实例
final GetIt getIt = GetIt.instance;

/// 注册安全教育模块依赖
void initSafetyInjection() {
  // 数据源 - 临时使用调试版本
  getIt.registerLazySingleton<SafetyExamDataSource>(
    () => DebugSafetyExamDataSource(
      dioClient: getIt<DioClient>(),
    ),
  );

  // 仓库
  getIt.registerLazySingleton<SafetyExamRepository>(
    () => SafetyExamRepositoryImpl(
      dataSource: getIt<SafetyExamDataSource>(),
    ),
  );

  // 用例
  getIt.registerLazySingleton(
    () => GetExamQuestionsUseCase(
      repository: getIt<SafetyExamRepository>(),
    ),
  );

  // BLoC
  getIt.registerFactory(
    () => SafetyExamBloc(
      getExamQuestionsUseCase: getIt<GetExamQuestionsUseCase>(),
    ),
  );
}
