/// -----
/// debug_safety_exam_data_source.dart
/// 
/// 调试版本的安全教育考试数据源
/// 用于调试API响应格式问题
///
/// <AUTHOR>
/// @date 2025-06-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/data/datasources/safety_exam_data_source.dart';

/// 调试版本的远程安全教育考试数据源
///
/// 包含详细的调试信息，用于分析API响应格式
class DebugSafetyExamDataSource implements SafetyExamDataSource {
  /// 网络客户端
  final DioClient dioClient;

  /// 构造函数
  DebugSafetyExamDataSource({
    required this.dioClient,
  });

  @override
  Future<List<SafetyExamQuestion>> getExamQuestions({String? planId}) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> queryParams = {};
      if (planId != null) {
        queryParams['planId'] = planId;
      }

      print('🔍 [DEBUG] 请求参数: $queryParams');
      print('🔍 [DEBUG] 请求URL: internshipservice/v1/internship/student/examQuestion/list');

      // 发送GET请求
      final response = await dioClient.get(
        'internshipservice/v1/internship/student/examQuestion/list',
        queryParameters: queryParams,
      );

      print('🔍 [DEBUG] ===== API响应分析 =====');
      print('🔍 [DEBUG] 响应类型: ${response.runtimeType}');
      print('🔍 [DEBUG] 响应内容: $response');
      
      if (response is Map) {
        print('🔍 [DEBUG] Map键: ${response.keys}');
        if (response.containsKey('data')) {
          print('🔍 [DEBUG] data字段类型: ${response['data'].runtimeType}');
          print('🔍 [DEBUG] data字段内容: ${response['data']}');
        }
        if (response.containsKey('resultCode')) {
          print('🔍 [DEBUG] resultCode: ${response['resultCode']}');
        }
        if (response.containsKey('resultMsg')) {
          print('🔍 [DEBUG] resultMsg: ${response['resultMsg']}');
        }
      } else if (response is List) {
        print('🔍 [DEBUG] List长度: ${response.length}');
        if (response.isNotEmpty) {
          print('🔍 [DEBUG] 第一个元素类型: ${response.first.runtimeType}');
          print('🔍 [DEBUG] 第一个元素内容: ${response.first}');
        }
      }
      print('🔍 [DEBUG] ========================');

      // 解析响应数据
      List<dynamic> data;
      
      if (response is List) {
        print('🔍 [DEBUG] 使用List模式解析');
        data = response;
      } else if (response is Map && response.containsKey('data')) {
        print('🔍 [DEBUG] 使用Map.data模式解析');
        data = response['data'] as List<dynamic>;
      } else {
        print('❌ [DEBUG] 无法识别的响应格式');
        throw Exception('API响应格式不正确: 期望List或包含data字段的Map，实际收到: ${response.runtimeType}');
      }

      print('🔍 [DEBUG] 题目数据长度: ${data.length}');
      
      // 转换为SafetyExamQuestion对象列表
      final List<SafetyExamQuestion> questions = [];
      for (int i = 0; i < data.length; i++) {
        try {
          final questionData = data[i] as Map<String, dynamic>;
          print('🔍 [DEBUG] 解析题目 ${i + 1}:');
          print('🔍 [DEBUG] - 题目字段: ${questionData.keys}');
          print('🔍 [DEBUG] - 题目ID: ${questionData['id']}');
          print('🔍 [DEBUG] - 题目标题: ${questionData['title']}');
          print('🔍 [DEBUG] - optionsJson: ${questionData['optionsJson']}');
          print('🔍 [DEBUG] - correctAnswer: ${questionData['correctAnswer']}');
          
          final question = SafetyExamQuestion.fromJson(questionData, i + 1);
          questions.add(question);
          print('✅ [DEBUG] 题目 ${i + 1} 解析成功');
        } catch (e) {
          print('❌ [DEBUG] 解析题目 ${i + 1} 失败: $e');
          print('❌ [DEBUG] 题目原始数据: ${data[i]}');
          rethrow;
        }
      }

      print('✅ [DEBUG] 成功解析 ${questions.length} 道题目');
      return questions;
    } catch (e) {
      print('❌ [DEBUG] 获取考试题目失败: $e');
      print('❌ [DEBUG] 错误类型: ${e.runtimeType}');
      if (e is Exception) {
        print('❌ [DEBUG] 异常详情: ${e.toString()}');
      }
      throw Exception('获取考试题目失败: ${e.toString()}');
    }
  }
}
