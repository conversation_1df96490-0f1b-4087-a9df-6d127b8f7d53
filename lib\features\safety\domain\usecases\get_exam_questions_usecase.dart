/// -----
/// get_exam_questions_usecase.dart
/// 
/// 获取安全教育考试题目用例
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/domain/repositories/safety_exam_repository.dart';

/// 获取安全教育考试题目用例
///
/// 封装获取安全教育考试题目的业务逻辑
class GetExamQuestionsUseCase {
  /// 安全教育考试仓库
  final SafetyExamRepository repository;

  /// 构造函数
  const GetExamQuestionsUseCase({
    required this.repository,
  });

  /// 执行用例，获取安全教育考试题目列表
  Future<List<SafetyExamQuestion>> call() async {
    return await repository.getExamQuestions();
  }
}
