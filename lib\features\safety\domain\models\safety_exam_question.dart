/// -----
/// safety_exam_question.dart
/// 
/// 安全教育考试题目模型
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 安全教育考试题目选项模型
///
/// 表示试题中的一个选项，包含选项标识和内容
class ExamOption extends Equatable {
  /// 选项标识，如A、B、C、D
  final String id;
  
  /// 选项内容
  final String content;

  /// 构造函数
  const ExamOption({
    required this.id,
    required this.content,
  });

  @override
  List<Object?> get props => [id, content];

  /// 从JSON映射创建选项对象
  factory ExamOption.fromJson(Map<String, dynamic> json) {
    return ExamOption(
      id: json['id'] as String,
      content: json['content'] as String,
    );
  }

  /// 将选项对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
    };
  }
}

/// 安全教育考试题目类型枚举
enum QuestionType {
  /// 单选题
  singleChoice,
  
  /// 多选题
  multipleChoice,
  
  /// 判断题
  judgment,
  
  /// 填空题
  fillBlank,
}

/// 安全教育考试题目模型
///
/// 表示一道完整的考试题目，包含题目内容、选项、答案等信息
class SafetyExamQuestion extends Equatable {
  /// 题目ID
  final String id;
  
  /// 题目类型
  final QuestionType type;
  
  /// 题目内容
  final String content;
  
  /// 题目选项列表
  final List<ExamOption> options;
  
  /// 正确答案（选项ID）
  final String correctAnswer;
  
  /// 题目序号
  final int number;

  /// 构造函数
  const SafetyExamQuestion({
    required this.id,
    required this.type,
    required this.content,
    required this.options,
    required this.correctAnswer,
    required this.number,
  });

  @override
  List<Object?> get props => [id, type, content, options, correctAnswer, number];

  /// 从JSON映射创建题目对象
  factory SafetyExamQuestion.fromJson(Map<String, dynamic> json) {
    return SafetyExamQuestion(
      id: json['id'] as String,
      type: _parseQuestionType(json['type'] as String),
      content: json['content'] as String,
      options: (json['options'] as List)
          .map((e) => ExamOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      correctAnswer: json['correctAnswer'] as String,
      number: json['number'] as int,
    );
  }

  /// 将题目对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': _questionTypeToString(type),
      'content': content,
      'options': options.map((e) => e.toJson()).toList(),
      'correctAnswer': correctAnswer,
      'number': number,
    };
  }

  /// 解析题目类型字符串为枚举值
  static QuestionType _parseQuestionType(String typeStr) {
    switch (typeStr) {
      case 'singleChoice':
        return QuestionType.singleChoice;
      case 'multipleChoice':
        return QuestionType.multipleChoice;
      case 'judgment':
        return QuestionType.judgment;
      case 'fillBlank':
        return QuestionType.fillBlank;
      default:
        return QuestionType.singleChoice;
    }
  }

  /// 将题目类型枚举值转换为字符串
  static String _questionTypeToString(QuestionType type) {
    switch (type) {
      case QuestionType.singleChoice:
        return 'singleChoice';
      case QuestionType.multipleChoice:
        return 'multipleChoice';
      case QuestionType.judgment:
        return 'judgment';
      case QuestionType.fillBlank:
        return 'fillBlank';
    }
  }
}
